#!/usr/bin/env python3
"""
对比 vLLM 实际结果和 Vidur 模拟结果的差异
按 request_id 匹配，计算 request_e2e_time 的相差比例
"""

import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def find_latest_results():
    """查找最新的 vLLM 和 Vidur 结果文件"""
    
    # 查找 vLLM 结果文件
    vllm_pattern = "validation/results/vllm/vllm_results_*.csv"
    vllm_files = glob.glob(vllm_pattern)
    
    if not vllm_files:
        print(f"❌ 未找到 vLLM 结果文件: {vllm_pattern}")
        return None, None
    
    # 选择最新的 vLLM 文件
    vllm_file = max(vllm_files, key=os.path.getctime)
    print(f"📄 vLLM 结果文件: {vllm_file}")
    print(f"📄 vLLM 结果文件完整路径: {os.path.abspath(vllm_file)}")
    
    # 查找 Vidur 结果文件
    vidur_pattern = "validation/results/vidur/*/request_metrics.csv"
    vidur_files = glob.glob(vidur_pattern)
    
    if not vidur_files:
        print(f"❌ 未找到 Vidur 结果文件: {vidur_pattern}")
        return vllm_file, None
    
    # 选择最新的 Vidur 文件
    vidur_file = max(vidur_files, key=os.path.getctime)
    print(f"📄 Vidur 结果文件: {vidur_file}")
    print(f"📄 Vidur 结果文件完整路径: {os.path.abspath(vidur_file)}")
    
    return vllm_file, vidur_file

def load_vllm_results(file_path):
    """加载 vLLM 结果"""
    
    try:
        df = pd.read_csv(file_path)
        print(f"✅ vLLM 数据加载成功: {len(df)} 条记录")
        
        # 检查必需的列
        required_cols = ['request_id', 'request_e2e_time', 'num_prefill_tokens', 'num_decode_tokens']
        optional_cols = ['request_e2e_time_normalized']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ vLLM 数据缺少列: {missing_cols}")
            print(f"   可用列: {list(df.columns)}")
            return None
        
        # 检查可选列并添加缺失的列
        for col in optional_cols:
            if col not in df.columns:
                if col == 'request_e2e_time_normalized' and 'num_decode_tokens' in df.columns:
                    # 计算 normalized 版本
                    df['request_e2e_time_normalized'] = df['request_e2e_time'] / df['num_decode_tokens']
                    print(f"   自动计算 {col} 列")
        
        # 显示数据概览
        print(f"   request_id 范围: {df['request_id'].min()} - {df['request_id'].max()}")
        print(f"   request_e2e_time 范围: {df['request_e2e_time'].min():.3f}s - {df['request_e2e_time'].max():.3f}s")
        if 'request_e2e_time_normalized' in df.columns:
            print(f"   request_e2e_time_normalized 范围: {df['request_e2e_time_normalized'].min():.6f}s - {df['request_e2e_time_normalized'].max():.6f}s")
        
        return df
        
    except Exception as e:
        print(f"❌ 加载 vLLM 结果失败: {e}")
        return None

def load_vidur_results(file_path):
    """加载 Vidur 结果"""
    
    try:
        df = pd.read_csv(file_path)
        print(f"✅ Vidur 数据加载成功: {len(df)} 条记录")
        
        # 检查必需的列
        required_cols = ['Request Id', 'request_e2e_time']
        optional_cols = ['request_e2e_time_normalized', 'request_num_decode_tokens']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ Vidur 数据缺少列: {missing_cols}")
            print(f"   可用列: {list(df.columns)}")
            return None
        
        # 重命名列以便匹配
        df = df.rename(columns={'Request Id': 'request_id'})
        
        # 检查可选列并添加缺失的列
        for col in optional_cols:
            if col not in df.columns:
                if col == 'request_e2e_time_normalized' and 'request_num_decode_tokens' in df.columns:
                    # 计算 normalized 版本
                    df['request_e2e_time_normalized'] = df['request_e2e_time'] / df['request_num_decode_tokens']
                    print(f"   自动计算 {col} 列")
        
        # 显示数据概览
        print(f"   request_id 范围: {df['request_id'].min()} - {df['request_id'].max()}")
        print(f"   request_e2e_time 范围: {df['request_e2e_time'].min():.3f}s - {df['request_e2e_time'].max():.3f}s")
        if 'request_e2e_time_normalized' in df.columns:
            print(f"   request_e2e_time_normalized 范围: {df['request_e2e_time_normalized'].min():.6f}s - {df['request_e2e_time_normalized'].max():.6f}s")
        
        return df
        
    except Exception as e:
        print(f"❌ 加载 Vidur 结果失败: {e}")
        return None

def compare_results(vllm_df, vidur_df):
    """对比 vLLM 和 Vidur 的结果"""
    
    print(f"\n📊 开始对比分析...")
    
    # 准备合并的列
    vllm_cols = ['request_id', 'request_e2e_time', 'num_prefill_tokens', 'num_decode_tokens']
    vidur_cols = ['request_id', 'request_e2e_time']
    
    # 如果有normalized列，也包含进来
    if 'request_e2e_time_normalized' in vllm_df.columns:
        vllm_cols.append('request_e2e_time_normalized')
    if 'request_e2e_time_normalized' in vidur_df.columns:
        vidur_cols.append('request_e2e_time_normalized')
    
    # 按 request_id 合并数据
    merged_df = pd.merge(
        vllm_df[vllm_cols], 
        vidur_df[vidur_cols], 
        on='request_id', 
        suffixes=('_vllm', '_vidur')
    )
    
    print(f"✅ 成功匹配 {len(merged_df)} 条记录")
    
    if len(merged_df) == 0:
        print("❌ 没有匹配的记录，无法进行对比")
        return None
    
    # 计算 e2e_time 差异
    merged_df['absolute_diff'] = merged_df['request_e2e_time_vidur'] - merged_df['request_e2e_time_vllm']
    merged_df['relative_diff_pct'] = (merged_df['absolute_diff'] / merged_df['request_e2e_time_vllm']) * 100
    merged_df['absolute_relative_diff_pct'] = np.abs(merged_df['relative_diff_pct'])
    
    # 如果有normalized数据，也计算差异
    if 'request_e2e_time_normalized_vllm' in merged_df.columns and 'request_e2e_time_normalized_vidur' in merged_df.columns:
        merged_df['absolute_diff_normalized'] = merged_df['request_e2e_time_normalized_vidur'] - merged_df['request_e2e_time_normalized_vllm']
        merged_df['relative_diff_pct_normalized'] = (merged_df['absolute_diff_normalized'] / merged_df['request_e2e_time_normalized_vllm']) * 100
        merged_df['absolute_relative_diff_pct_normalized'] = np.abs(merged_df['relative_diff_pct_normalized'])
        print(f"✅ 同时计算了 normalized 指标的差异")
    
    return merged_df

def analyze_differences(merged_df):
    """分析差异统计"""
    
    print(f"\n📈 差异分析结果:")
    print("=" * 50)
    
    # 基本统计 - E2E Time
    vllm_mean = merged_df['request_e2e_time_vllm'].mean()
    vidur_mean = merged_df['request_e2e_time_vidur'].mean()
    
    print(f"📊 E2E Time 基本统计:")
    print(f"   vLLM 平均 e2e 时间: {vllm_mean:.3f}s")
    print(f"   Vidur 平均 e2e 时间: {vidur_mean:.3f}s")
    print(f"   整体平均差异: {vidur_mean - vllm_mean:.3f}s ({((vidur_mean - vllm_mean) / vllm_mean) * 100:+.1f}%)")
    
    # E2E Time 相对差异统计
    relative_diff = merged_df['relative_diff_pct']
    abs_relative_diff = merged_df['absolute_relative_diff_pct']
    
    print(f"\n📊 E2E Time 相对差异统计 (%):")
    print(f"   平均相对差异: {relative_diff.mean():+.1f}%")
    print(f"   平均绝对相对差异: {abs_relative_diff.mean():.1f}%")
    print(f"   相对差异标准差: {relative_diff.std():.1f}%")
    print(f"   相对差异范围: {relative_diff.min():+.1f}% 到 {relative_diff.max():+.1f}%")
    
    # 如果有normalized数据，也分析
    stats = {}
    if 'request_e2e_time_normalized_vllm' in merged_df.columns and 'request_e2e_time_normalized_vidur' in merged_df.columns:
        vllm_mean_norm = merged_df['request_e2e_time_normalized_vllm'].mean()
        vidur_mean_norm = merged_df['request_e2e_time_normalized_vidur'].mean()
        
        print(f"\n📊 E2E Time Normalized 基本统计:")
        print(f"   vLLM 平均 e2e 时间 (normalized): {vllm_mean_norm:.6f}s")
        print(f"   Vidur 平均 e2e 时间 (normalized): {vidur_mean_norm:.6f}s")
        print(f"   整体平均差异 (normalized): {vidur_mean_norm - vllm_mean_norm:.6f}s ({((vidur_mean_norm - vllm_mean_norm) / vllm_mean_norm) * 100:+.1f}%)")
        
        relative_diff_norm = merged_df['relative_diff_pct_normalized']
        abs_relative_diff_norm = merged_df['absolute_relative_diff_pct_normalized']
        
        print(f"\n📊 E2E Time Normalized 相对差异统计 (%):")
        print(f"   平均相对差异: {relative_diff_norm.mean():+.1f}%")
        print(f"   平均绝对相对差异: {abs_relative_diff_norm.mean():.1f}%")
        print(f"   相对差异标准差: {relative_diff_norm.std():.1f}%")
        print(f"   相对差异范围: {relative_diff_norm.min():+.1f}% 到 {relative_diff_norm.max():+.1f}%")
        
        stats['normalized'] = {
            'mean_relative_diff': relative_diff_norm.mean(),
            'mean_abs_relative_diff': abs_relative_diff_norm.mean(),
            'std_relative_diff': relative_diff_norm.std(),
        }
    
    # 分位数分析 - E2E Time
    print(f"\n📊 E2E Time 相对差异分位数:")
    percentiles = [10, 25, 50, 75, 90, 95, 99]
    for p in percentiles:
        value = np.percentile(abs_relative_diff, p)
        print(f"   P{p}: {value:.1f}%")
    
    # Normalized分位数分析
    if 'normalized' in stats:
        abs_relative_diff_norm = merged_df['absolute_relative_diff_pct_normalized']
        print(f"\n📊 E2E Time Normalized 相对差异分位数:")
        for p in percentiles:
            value = np.percentile(abs_relative_diff_norm, p)
            print(f"   P{p}: {value:.1f}%")
    
    # 差异分布 - E2E Time
    print(f"\n📊 E2E Time 差异分布:")
    ranges = [(0, 5), (5, 10), (10, 20), (20, 50), (50, float('inf'))]
    for low, high in ranges:
        if high == float('inf'):
            count = (abs_relative_diff >= low).sum()
            pct = (count / len(abs_relative_diff)) * 100
            print(f"   ≥{low}%: {count} 条 ({pct:.1f}%)")
        else:
            count = ((abs_relative_diff >= low) & (abs_relative_diff < high)).sum()
            pct = (count / len(abs_relative_diff)) * 100
            print(f"   {low}-{high}%: {count} 条 ({pct:.1f}%)")
    
    # Normalized差异分布
    if 'normalized' in stats:
        print(f"\n📊 E2E Time Normalized 差异分布:")
        for low, high in ranges:
            if high == float('inf'):
                count = (abs_relative_diff_norm >= low).sum()
                pct = (count / len(abs_relative_diff_norm)) * 100
                print(f"   ≥{low}%: {count} 条 ({pct:.1f}%)")
            else:
                count = ((abs_relative_diff_norm >= low) & (abs_relative_diff_norm < high)).sum()
                pct = (count / len(abs_relative_diff_norm)) * 100
                print(f"   {low}-{high}%: {count} 条 ({pct:.1f}%)")
    
    # 保真度评估 - E2E Time
    print(f"\n🎯 E2E Time 保真度评估:")
    excellent = (abs_relative_diff < 5).sum()
    good = ((abs_relative_diff >= 5) & (abs_relative_diff < 10)).sum()
    fair = ((abs_relative_diff >= 10) & (abs_relative_diff < 20)).sum()
    poor = (abs_relative_diff >= 20).sum()
    
    total = len(abs_relative_diff)
    print(f"   优秀 (<5%): {excellent} 条 ({excellent/total*100:.1f}%)")
    print(f"   良好 (5-10%): {good} 条 ({good/total*100:.1f}%)")
    print(f"   一般 (10-20%): {fair} 条 ({fair/total*100:.1f}%)")
    print(f"   较差 (≥20%): {poor} 条 ({poor/total*100:.1f}%)")
    
    # Normalized保真度评估
    if 'normalized' in stats:
        print(f"\n🎯 E2E Time Normalized 保真度评估:")
        excellent_norm = (abs_relative_diff_norm < 5).sum()
        good_norm = ((abs_relative_diff_norm >= 5) & (abs_relative_diff_norm < 10)).sum()
        fair_norm = ((abs_relative_diff_norm >= 10) & (abs_relative_diff_norm < 20)).sum()
        poor_norm = (abs_relative_diff_norm >= 20).sum()
        
        print(f"   优秀 (<5%): {excellent_norm} 条 ({excellent_norm/total*100:.1f}%)")
        print(f"   良好 (5-10%): {good_norm} 条 ({good_norm/total*100:.1f}%)")
        print(f"   一般 (10-20%): {fair_norm} 条 ({fair_norm/total*100:.1f}%)")
        print(f"   较差 (≥20%): {poor_norm} 条 ({poor_norm/total*100:.1f}%)")
        
        stats['normalized'].update({
            'excellent_pct': excellent_norm/total*100,
            'good_pct': good_norm/total*100,
            'fair_pct': fair_norm/total*100,
            'poor_pct': poor_norm/total*100
        })
    
    stats.update({
        'mean_relative_diff': relative_diff.mean(),
        'mean_abs_relative_diff': abs_relative_diff.mean(),
        'std_relative_diff': relative_diff.std(),
        'excellent_pct': excellent/total*100,
        'good_pct': good/total*100,
        'fair_pct': fair/total*100,
        'poor_pct': poor/total*100
    })
    
    return stats

def create_visualizations(merged_df, output_dir):
    """创建可视化图表"""

    os.makedirs(output_dir, exist_ok=True)
    
    # 设置图表样式
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. 散点图：vLLM vs Vidur (E2E Time)
    fig_width = 20 if 'request_e2e_time_normalized_vllm' in merged_df.columns else 10
    fig, axes = plt.subplots(1, 2 if 'request_e2e_time_normalized_vllm' in merged_df.columns else 1, 
                            figsize=(fig_width, 8))
    
    if 'request_e2e_time_normalized_vllm' in merged_df.columns:
        ax1, ax2 = axes
    else:
        ax1 = axes
    
    # E2E Time 散点图
    ax1.scatter(merged_df['request_e2e_time_vllm'], merged_df['request_e2e_time_vidur'], 
                alpha=0.6, s=30)
    
    # 添加对角线（完美匹配线）
    min_val = min(merged_df['request_e2e_time_vllm'].min(), merged_df['request_e2e_time_vidur'].min())
    max_val = max(merged_df['request_e2e_time_vllm'].max(), merged_df['request_e2e_time_vidur'].max())
    ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='Perfect Match Line')
    
    ax1.set_xlabel('vLLM request_e2e_time (s)')
    ax1.set_ylabel('Vidur request_e2e_time (s)')
    ax1.set_title('vLLM vs Vidur End-to-End Time Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Normalized 散点图
    if 'request_e2e_time_normalized_vllm' in merged_df.columns:
        ax2.scatter(merged_df['request_e2e_time_normalized_vllm'], merged_df['request_e2e_time_normalized_vidur'], 
                    alpha=0.6, s=30, color='orange')
        
        # 添加对角线（完美匹配线）
        min_val_norm = min(merged_df['request_e2e_time_normalized_vllm'].min(), merged_df['request_e2e_time_normalized_vidur'].min())
        max_val_norm = max(merged_df['request_e2e_time_normalized_vllm'].max(), merged_df['request_e2e_time_normalized_vidur'].max())
        ax2.plot([min_val_norm, max_val_norm], [min_val_norm, max_val_norm], 'r--', alpha=0.8, label='Perfect Match Line')
        
        ax2.set_xlabel('vLLM request_e2e_time_normalized (s)')
        ax2.set_ylabel('Vidur request_e2e_time_normalized (s)')
        ax2.set_title('vLLM vs Vidur End-to-End Time Comparison (Normalized)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/vllm_vs_vidur_scatter.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 相对差异分布直方图
    num_plots = 4 if 'request_e2e_time_normalized_vllm' in merged_df.columns else 2
    fig, axes = plt.subplots(2, 2 if num_plots == 4 else 1, figsize=(12, 10 if num_plots == 4 else 6))
    
    if num_plots == 4:
        ((ax1, ax2), (ax3, ax4)) = axes
    else:
        ax1, ax2 = axes
    
    # E2E Time 相对差异
    ax1.hist(merged_df['relative_diff_pct'], bins=50, alpha=0.7, edgecolor='black')
    ax1.axvline(0, color='red', linestyle='--', alpha=0.8, label='Zero Difference Line')
    ax1.set_xlabel('E2E Time Relative Difference (%)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('E2E Time Relative Difference Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # E2E Time 绝对相对差异
    ax2.hist(merged_df['absolute_relative_diff_pct'], bins=50, alpha=0.7, edgecolor='black')
    ax2.set_xlabel('E2E Time Absolute Relative Difference (%)')
    ax2.set_ylabel('Frequency')
    ax2.set_title('E2E Time Absolute Relative Difference Distribution')
    ax2.grid(True, alpha=0.3)
    
    # Normalized 相对差异
    if num_plots == 4:
        ax3.hist(merged_df['relative_diff_pct_normalized'], bins=50, alpha=0.7, edgecolor='black', color='orange')
        ax3.axvline(0, color='red', linestyle='--', alpha=0.8, label='Zero Difference Line')
        ax3.set_xlabel('E2E Time Normalized Relative Difference (%)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('E2E Time Normalized Relative Difference Distribution')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Normalized 绝对相对差异
        ax4.hist(merged_df['absolute_relative_diff_pct_normalized'], bins=50, alpha=0.7, edgecolor='black', color='orange')
        ax4.set_xlabel('E2E Time Normalized Absolute Relative Difference (%)')
        ax4.set_ylabel('Frequency')
        ax4.set_title('E2E Time Normalized Absolute Relative Difference Distribution')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/relative_diff_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 箱线图
    fig, axes = plt.subplots(1, 2 if 'request_e2e_time_normalized_vllm' in merged_df.columns else 1, 
                            figsize=(15 if 'request_e2e_time_normalized_vllm' in merged_df.columns else 10, 6))
    
    if 'request_e2e_time_normalized_vllm' in merged_df.columns:
        ax1, ax2 = axes
    else:
        ax1 = axes
    
    # E2E Time 箱线图
    data_to_plot = [merged_df['request_e2e_time_vllm'], merged_df['request_e2e_time_vidur']]
    labels = ['vLLM', 'Vidur']
    
    ax1.boxplot(data_to_plot, labels=labels)
    ax1.set_ylabel('request_e2e_time (s)')
    ax1.set_title('vLLM vs Vidur 端到端时间分布对比')
    ax1.grid(True, alpha=0.3)
    
    # Normalized 箱线图
    if 'request_e2e_time_normalized_vllm' in merged_df.columns:
        data_to_plot_norm = [merged_df['request_e2e_time_normalized_vllm'], merged_df['request_e2e_time_normalized_vidur']]
        
        ax2.boxplot(data_to_plot_norm, labels=labels)
        ax2.set_ylabel('request_e2e_time_normalized (s)')
        ax2.set_title('vLLM vs Vidur 端到端时间分布对比 (Normalized)')
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/boxplot_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 图表已保存到: {output_dir}/")

def save_detailed_results(merged_df, stats, output_dir, vllm_file, vidur_file):
    """保存详细结果"""

    os.makedirs(output_dir, exist_ok=True)

    # 保存详细对比数据
    detailed_file = f"{output_dir}/detailed_comparison.csv"
    merged_df.to_csv(detailed_file, index=False)
    print(f"📄 详细对比数据已保存: {detailed_file}")

    # 保存统计摘要
    summary_file = f"{output_dir}/comparison_summary.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("vLLM vs Vidur 对比分析摘要\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"vLLM 数据文件: {vllm_file}\n")
        f.write(f"Vidur 数据文件: {vidur_file}\n\n")
        f.write(f"总记录数: {len(merged_df)}\n")
        f.write(f"平均相对差异: {stats['mean_relative_diff']:+.1f}%\n")
        f.write(f"平均绝对相对差异: {stats['mean_abs_relative_diff']:.1f}%\n")
        f.write(f"相对差异标准差: {stats['std_relative_diff']:.1f}%\n\n")
        f.write("保真度评估:\n")
        f.write(f"  优秀 (<5%): {stats['excellent_pct']:.1f}%\n")
        f.write(f"  良好 (5-10%): {stats['good_pct']:.1f}%\n")
        f.write(f"  一般 (10-20%): {stats['fair_pct']:.1f}%\n")
        f.write(f"  较差 (≥20%): {stats['poor_pct']:.1f}%\n")

    print(f"📄 统计摘要已保存: {summary_file}")

    # 保存源文件信息
    info_file = f"{output_dir}/comparison_info.txt"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write("对比分析信息\n")
        f.write("=" * 20 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"输出目录: {output_dir}\n\n")
        f.write("数据源文件:\n")
        f.write(f"  vLLM 结果: {vllm_file}\n")
        f.write(f"  Vidur 结果: {vidur_file}\n\n")
        f.write("生成的文件:\n")
        f.write(f"  - detailed_comparison.csv: 详细对比数据\n")
        f.write(f"  - comparison_summary.txt: 统计摘要\n")
        f.write(f"  - vllm_vs_vidur_scatter.png: 散点图对比\n")
        f.write(f"  - relative_diff_distribution.png: 差异分布图\n")
        f.write(f"  - boxplot_comparison.png: 箱线图对比\n")
        f.write(f"  - COMPARISON_REPORT.md: 完整分析报告\n")

    print(f"📄 对比信息已保存: {info_file}")

    # 保存完整分析报告
    report_file = f"{output_dir}/COMPARISON_REPORT.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"# vLLM vs Vidur 性能对比分析报告\n\n")
        f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"## 📊 **对比结果摘要**\n\n")
        f.write(f"### 数据源\n")
        f.write(f"- **vLLM 结果文件**: `{os.path.basename(vllm_file)}`\n")
        f.write(f"- **Vidur 结果文件**: `{os.path.basename(vidur_file)}`\n\n")
        f.write(f"### 基本统计\n")
        f.write(f"- **总记录数**: {len(merged_df)} 条\n")
        vllm_mean = merged_df['request_e2e_time_vllm'].mean()
        vidur_mean = merged_df['request_e2e_time_vidur'].mean()
        f.write(f"- **vLLM 平均 e2e 时间**: {vllm_mean:.3f}s\n")
        f.write(f"- **Vidur 平均 e2e 时间**: {vidur_mean:.3f}s\n")
        f.write(f"- **整体平均差异**: {vidur_mean - vllm_mean:.3f}s ({((vidur_mean - vllm_mean) / vllm_mean) * 100:+.1f}%)\n\n")
        f.write(f"### 关键发现\n")
        f.write(f"- **平均绝对相对差异**: {stats['mean_abs_relative_diff']:.1f}%\n")
        f.write(f"- **相对差异标准差**: {stats['std_relative_diff']:.1f}%\n")
        relative_diff = merged_df['relative_diff_pct']
        f.write(f"- **差异范围**: {relative_diff.min():+.1f}% 到 {relative_diff.max():+.1f}%\n\n")
        f.write(f"## 🎯 **保真度评估**\n\n")
        f.write(f"| 差异范围 | 记录数 | 占比 | 评级 |\n")
        f.write(f"|---------|--------|------|---------|\n")
        f.write(f"| < 5% | {int(stats['excellent_pct'] * len(merged_df) / 100)} 条 | {stats['excellent_pct']:.1f}% | 优秀 |\n")
        f.write(f"| 5-10% | {int(stats['good_pct'] * len(merged_df) / 100)} 条 | {stats['good_pct']:.1f}% | 良好 |\n")
        f.write(f"| 10-20% | {int(stats['fair_pct'] * len(merged_df) / 100)} 条 | {stats['fair_pct']:.1f}% | 一般 |\n")
        f.write(f"| ≥ 20% | {int(stats['poor_pct'] * len(merged_df) / 100)} 条 | {stats['poor_pct']:.1f}% | 较差 |\n\n")
        f.write(f"## 📈 **差异分布分析**\n\n")
        abs_relative_diff = merged_df['absolute_relative_diff_pct']
        percentiles = [50, 75, 90, 95, 99]
        f.write(f"### 分位数分析\n")
        for p in percentiles:
            value = np.percentile(abs_relative_diff, p)
            f.write(f"- **P{p}**: {value:.1f}%\n")
        f.write(f"\n### 主要观察\n")
        if stats['mean_relative_diff'] < 0:
            f.write(f"1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 {abs(stats['mean_relative_diff']):.1f}%）\n")
        else:
            f.write(f"1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 {stats['mean_relative_diff']:.1f}%）\n")
        f.write(f"2. **差异稳定**: 标准差 {stats['std_relative_diff']:.1f}%，说明偏差相对稳定\n")
        f.write(f"3. **影响范围**: {stats['poor_pct']:.1f}% 的请求差异超过 20%\n\n")
        f.write(f"## 💡 **结论**\n\n")
        if stats['mean_abs_relative_diff'] < 10:
            f.write(f"🎉 **Vidur 显示出良好的保真度！** 平均差异 {stats['mean_abs_relative_diff']:.1f}% 在可接受范围内。\n")
        elif stats['mean_abs_relative_diff'] < 20:
            f.write(f"👍 **Vidur 显示出可接受的保真度**，平均差异 {stats['mean_abs_relative_diff']:.1f}%，有改进空间。\n")
        else:
            f.write(f"⚠️ **Vidur 的保真度有待改进**，平均差异 {stats['mean_abs_relative_diff']:.1f}% 超出理想范围。\n")
        f.write(f"\n---\n")
        f.write(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")

    print(f"📄 完整分析报告已保存: {report_file}")

def main():
    """主函数"""
    print("🔍 vLLM vs Vidur 结果对比分析")
    print("=" * 60)

    # 创建带时间戳的输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"validation/results/comparison/comparison_{timestamp}"
    print(f"📁 输出目录: {output_dir}")

    # 查找结果文件
    vllm_file, vidur_file = find_latest_results()
    
    if not vllm_file or not vidur_file:
        print("❌ 缺少必要的结果文件，无法进行对比")
        return
    
    # 加载数据
    print(f"\n📂 加载数据...")
    vllm_df = load_vllm_results(vllm_file)
    vidur_df = load_vidur_results(vidur_file)
    
    if vllm_df is None or vidur_df is None:
        print("❌ 数据加载失败，无法进行对比")
        return
    
    # 对比结果
    merged_df = compare_results(vllm_df, vidur_df)
    
    if merged_df is None:
        return
    
    # 分析差异
    stats = analyze_differences(merged_df)
    
    # 创建可视化
    print(f"\n📊 生成可视化图表...")
    create_visualizations(merged_df, output_dir)

    # 保存结果
    print(f"\n💾 保存详细结果...")
    save_detailed_results(merged_df, stats, output_dir, vllm_file, vidur_file)
    
    # 总结
    print(f"\n🎯 总结:")
    print(f"✅ 成功对比了 {len(merged_df)} 条记录")
    print(f"📊 E2E Time 平均绝对相对差异: {stats['mean_abs_relative_diff']:.1f}%")
    
    if 'normalized' in stats:
        print(f"📊 E2E Time Normalized 平均绝对相对差异: {stats['normalized']['mean_abs_relative_diff']:.1f}%")
    
    if stats['mean_abs_relative_diff'] < 10:
        print("🎉 Vidur E2E Time 显示出良好的保真度！")
    elif stats['mean_abs_relative_diff'] < 20:
        print("👍 Vidur E2E Time 显示出可接受的保真度")
    else:
        print("⚠️  Vidur E2E Time 的保真度有待改进")
        
    if 'normalized' in stats:
        if stats['normalized']['mean_abs_relative_diff'] < 10:
            print("🎉 Vidur E2E Time Normalized 显示出良好的保真度！")
        elif stats['normalized']['mean_abs_relative_diff'] < 20:
            print("👍 Vidur E2E Time Normalized 显示出可接受的保真度")
        else:
            print("⚠️  Vidur E2E Time Normalized 的保真度有待改进")

if __name__ == "__main__":
    main()
