#!/bin/bash

# CPU Profiling启动脚本
# 使用sarathi-serve的profiling模块测量CPU overhead
#
# 配置说明：
# - 所有关键参数与 validation/configs/config_shared.yaml 保持一致
# - 模型路径与 validation/configs/config_vllm.yaml 保持一致
# - 输出目录结构遵循 vidur 标准：data/profiling/cpu_overhead/{NETWORK_DEVICE}/
#
# 使用方法：
# 1. 修改 MODELS 数组添加要profiling的模型
# 2. 根据需要调整 TENSOR_PARALLEL_WORKERS 和 MAX_BATCH_SIZE
# 3. 运行脚本：./run_cpu_profiling.sh

# 配置文件路径
CONFIG_SHARED="validation/configs/config_shared.yaml"
CONFIG_VLLM="validation/configs/config_vllm.yaml"
CONFIG_VIDUR="validation/configs/config_vidur.yaml"

# 全局变量（将从配置文件中读取）
DEVICE=""
NETWORK_DEVICE=""
TENSOR_PARALLEL_SIZE=""
PIPELINE_PARALLEL_SIZE=""
MAX_NUM_SEQS=""
MAX_NUM_BATCHED_TOKENS=""
BLOCK_SIZE=""
GPU_MEMORY_UTILIZATION=""
MAX_MODEL_LEN=""
DTYPE=""
MODEL_NAME=""
MODEL_PATH=""
MAX_BATCH_SIZE=""
OUTPUT_DIR=""
TENSOR_PARALLEL_WORKERS=()

# Python虚拟环境路径
PYTHON_ENV="./sarathi-env/bin/python"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载YAML解析脚本
if [ ! -f "validation/parse_yaml.sh" ]; then
    print_error "YAML解析脚本不存在: validation/parse_yaml.sh"
    exit 1
fi

source validation/parse_yaml.sh

# 加载配置文件
load_config() {
    print_info "从配置文件加载参数..."

    # 解析共享配置文件
    if ! parse_yaml "$CONFIG_SHARED" "SHARED"; then
        print_error "无法解析共享配置文件: $CONFIG_SHARED"
        exit 1
    fi

    # 解析vLLM配置文件
    if ! parse_yaml "$CONFIG_VLLM" "VLLM"; then
        print_error "无法解析vLLM配置文件: $CONFIG_VLLM"
        exit 1
    fi

    # 从解析的变量中提取配置
    DEVICE="$SHARED_device"
    NETWORK_DEVICE="$SHARED_network_device"
    TENSOR_PARALLEL_SIZE="$SHARED_tensor_parallel_size"
    PIPELINE_PARALLEL_SIZE="$SHARED_pipeline_parallel_size"
    MAX_NUM_SEQS="$SHARED_max_num_seqs"
    MAX_NUM_BATCHED_TOKENS="$SHARED_max_num_batched_tokens"
    BLOCK_SIZE="$SHARED_block_size"
    GPU_MEMORY_UTILIZATION="$SHARED_gpu_memory_utilization"
    MAX_MODEL_LEN="$SHARED_max_model_len"
    DTYPE="$SHARED_dtype"
    MODEL_NAME="$SHARED_model_name"
    MODEL_PATH="$VLLM_model"
    # prediction_max_batch_size 在 execution_time_predictor 节下
    MAX_BATCH_SIZE="$SHARED_execution_time_predictor_prediction_max_batch_size"

    # 检查必需的配置是否存在
    local required_vars=(
        "device" "network_device" "tensor_parallel_size" "max_num_seqs"
        "max_num_batched_tokens" "block_size" "gpu_memory_utilization"
        "max_model_len" "model_name"
    )

    if ! check_yaml_vars "SHARED" "${required_vars[@]}"; then
        print_error "共享配置文件缺少必需的配置项"
        exit 1
    fi

    # 检查嵌套配置项
    if [ -z "$SHARED_execution_time_predictor_prediction_max_batch_size" ]; then
        print_error "共享配置文件缺少 execution_time_predictor.prediction_max_batch_size 配置"
        exit 1
    fi

    if [ -z "$VLLM_model" ]; then
        print_error "vLLM配置文件缺少模型路径配置"
        exit 1
    fi

    # 设置派生参数
    OUTPUT_DIR="data/profiling/cpu_overhead/${NETWORK_DEVICE}"
    TENSOR_PARALLEL_WORKERS=($TENSOR_PARALLEL_SIZE)

    print_success "配置加载完成"
}

# 检查虚拟环境
check_environment() {
    if [ ! -f "$PYTHON_ENV" ]; then
        print_error "Python虚拟环境不存在: $PYTHON_ENV"
        print_info "请确保虚拟环境已正确安装"
        exit 1
    fi
    print_success "Python虚拟环境检查通过"
}

# 检查模型配置
check_model_config() {
    # 检查模型名称和路径是否已设置
    if [ -z "$MODEL_NAME" ] || [ -z "$MODEL_PATH" ]; then
        print_error "模型配置不完整"
        print_info "MODEL_NAME: $MODEL_NAME"
        print_info "MODEL_PATH: $MODEL_PATH"
        exit 1
    fi

    print_success "模型配置检查通过"
    print_info "模型名称: $MODEL_NAME"
    print_info "模型路径: $MODEL_PATH"
}

# 检查配置文件存在性
check_config_files() {
    print_info "检查配置文件..."

    if [ ! -f "$CONFIG_SHARED" ]; then
        print_error "共享配置文件不存在: $CONFIG_SHARED"
        print_info "请确保在项目根目录运行此脚本，且配置文件存在"
        exit 1
    else
        print_success "找到共享配置文件: $CONFIG_SHARED"
    fi

    if [ ! -f "$CONFIG_VLLM" ]; then
        print_error "vLLM配置文件不存在: $CONFIG_VLLM"
        print_info "请确保在项目根目录运行此脚本，且配置文件存在"
        exit 1
    else
        print_success "找到vLLM配置文件: $CONFIG_VLLM"
    fi

    if [ ! -f "$CONFIG_VIDUR" ]; then
        print_info "Vidur配置文件不存在: $CONFIG_VIDUR (可选)"
    else
        print_info "找到Vidur配置文件: $CONFIG_VIDUR"
    fi
}

# 显示配置信息
show_config() {
    print_info "=== CPU Profiling 配置信息 ==="
    echo -e "配置文件来源:"
    echo -e "  共享配置: ${YELLOW}$CONFIG_SHARED${NC}"
    echo -e "  vLLM配置: ${YELLOW}$CONFIG_VLLM${NC}"
    echo -e "  Vidur配置: ${YELLOW}$CONFIG_VIDUR${NC}"
    echo ""
    echo -e "设备配置:"
    echo -e "  设备类型: ${YELLOW}$DEVICE${NC}"
    echo -e "  网络设备: ${YELLOW}$NETWORK_DEVICE${NC}"
    echo -e "  张量并行度: ${YELLOW}$TENSOR_PARALLEL_SIZE${NC}"
    echo -e "  流水线并行度: ${YELLOW}$PIPELINE_PARALLEL_SIZE${NC}"
    echo ""
    echo -e "模型配置:"
    echo -e "  模型名称: ${YELLOW}$MODEL_NAME${NC}"
    echo -e "  模型路径: ${YELLOW}$MODEL_PATH${NC}"
    echo ""
    echo -e "性能配置:"
    echo -e "  最大序列数: ${YELLOW}$MAX_NUM_SEQS${NC}"
    echo -e "  最大批次Token数: ${YELLOW}$MAX_NUM_BATCHED_TOKENS${NC}"
    echo -e "  最大模型长度: ${YELLOW}$MAX_MODEL_LEN${NC}"
    echo -e "  GPU内存利用率: ${YELLOW}$GPU_MEMORY_UTILIZATION${NC}"
    echo -e "  KV Cache块大小: ${YELLOW}$BLOCK_SIZE${NC}"
    echo -e "  数据类型: ${YELLOW}$DTYPE${NC}"
    echo ""
    echo -e "Profiling配置:"
    echo -e "  张量并行度: ${YELLOW}${TENSOR_PARALLEL_WORKERS[@]}${NC}"
    echo -e "  最大Batch Size: ${YELLOW}$MAX_BATCH_SIZE${NC}"
    echo -e "  输出目录: ${YELLOW}$OUTPUT_DIR${NC}"
    echo -e "  Python环境: ${YELLOW}$PYTHON_ENV${NC}"
    echo "=================================="
}

# 构建模型名称参数字符串
build_model_names_args() {
    echo " $MODEL_NAME"
}

# 构建模型路径参数字符串
build_model_paths_args() {
    echo " $MODEL_PATH"
}

# 构建张量并行度参数字符串
build_tp_args() {
    local tp_str=""
    for tp in "${TENSOR_PARALLEL_WORKERS[@]}"; do
        tp_str="$tp_str $tp"
    done
    echo "$tp_str"
}

# 清理Ray进程
cleanup_ray() {
    print_info "清理现有Ray进程..."
    
    # 尝试优雅关闭用户Ray进程
    if pgrep -f "ray.*python" > /dev/null; then
        print_info "检测到Ray进程，尝试关闭..."
        eval "$PYTHON_ENV -c 'import ray; ray.shutdown()'" 2>/dev/null || true
        sleep 2
    fi
    
    # 设置Ray临时目录避免冲突
    export RAY_TMPDIR="/tmp/ray_vidur_$$"
    mkdir -p "$RAY_TMPDIR"
    
    print_success "Ray环境清理完成"
}

# 运行CPU profiling
run_profiling() {
    local model_names_args=$(build_model_names_args)
    local model_paths_args=$(build_model_paths_args)
    local tp_args=$(build_tp_args)

    print_info "开始运行CPU profiling..."
    print_info "这可能需要一些时间，请耐心等待..."

    # 清理Ray环境
    # cleanup_ray

    # 构建完整命令
    local cmd="$PYTHON_ENV -m vidur.profiling.cpu_overhead.main"
    cmd="$cmd --output_dir $OUTPUT_DIR"
    cmd="$cmd --model_names$model_names_args"
    cmd="$cmd --model_paths$model_paths_args"
    cmd="$cmd --num_tensor_parallel_workers$tp_args"
    cmd="$cmd --max_batch_size $MAX_BATCH_SIZE"
    
    print_info "执行命令: $cmd"
    echo ""
    
    # 设置环境变量避免Ray冲突
    export RAY_DEDUP_LOGS=0
    export RAY_DISABLE_IMPORT_WARNING=1
    
    # 执行命令
    if eval "$cmd"; then
        print_success "CPU profiling完成!"
        show_results
    else
        print_error "CPU profiling执行失败"
        print_warning "如果遇到Ray相关错误，请尝试重启系统或联系管理员"
        exit 1
    fi
}

# 显示结果信息
show_results() {
    print_info "=== 结果信息 ==="
    if [ -d "$OUTPUT_DIR" ]; then
        print_success "结果已保存到: $OUTPUT_DIR"
        
        # 查找最新的时间戳目录
        local latest_dir=$(find "$OUTPUT_DIR/cpu_overhead" -maxdepth 1 -type d -name "20*" | sort | tail -1)
        if [ -n "$latest_dir" ]; then
            print_info "最新结果目录: $latest_dir"
            
            # 显示生成的CSV文件
            local csv_files=$(find "$latest_dir" -name "*.csv" | wc -l)
            if [ $csv_files -gt 0 ]; then
                print_success "生成了 $csv_files 个CSV结果文件"
                print_info "CSV文件位置:"
                find "$latest_dir" -name "*.csv" | sed 's/^/  /'
            fi
        fi
    else
        print_warning "未找到输出目录"
    fi
}

# 清理函数
cleanup() {
    print_info "正在清理..."
    # 这里可以添加清理逻辑，如果需要的话
}

# 捕获中断信号
trap cleanup EXIT INT TERM

# 主函数
main() {
    print_info "启动CPU Profiling脚本"
    echo ""

    # 检查环境
    check_environment

    # 检查配置文件
    check_config_files

    # 加载配置
    load_config

    # 检查模型配置
    check_model_config

    # 显示配置
    show_config

    run_profiling
}

# 脚本入口
main "$@"