# 共享配置文件 - config_shared.yaml
# 包含 vidur 和 vLLM 都需要的通用参数

# 模型配置
model_name: "meta-llama/Meta-Llama-3-8B"
max_model_len: 4096
dtype: "auto"
trust_remote_code: false

# 并行配置
tensor_parallel_size: 1
pipeline_parallel_size: 1

# 设备配置
device: "a100"
gpu_memory_utilization: 0.9

# KV Cache 配置
block_size: 16
kv_cache_dtype: "auto"

# 调度配置 (vLLM 参数名 -> Vidur 参数名映射)
# max_num_seqs: vLLM --max-num-seqs -> Vidur --vllm_scheduler_config_batch_size_cap
max_num_seqs: 128
# max_num_batched_tokens: vLLM --max-num-batched-tokens -> Vidur --vllm_scheduler_config_max_tokens_in_batch
# 设置为与实际 vLLM 配置一致的值
max_num_batched_tokens: 4096
# batch_size_cap: 已废弃，使用 max_num_seqs
batch_size_cap: 128
# watermark_blocks_fraction: Vidur 特定参数
watermark_blocks_fraction: 0.01

# 随机种子
seed: 42

# 请求生成配置（用于测试）
num_requests: 512
qps: 4.0

# 网络设备配置（Vidur 特定）
network_device: "a100_pairwise_nvlink"

# 内存配置（Vidur 特定）
memory_margin_fraction: 0.1

# 注意：trace_file 不是共享参数，因为 vidur 和 vLLM 使用不同格式的数据文件

# 日志配置
log_level: "info"

# 执行时间预测器配置（Vidur 特定，但影响性能对比）
execution_time_predictor:
  type: "random_forrest"
  prediction_max_prefill_chunk_size: 4096
  prediction_max_batch_size: 256
  prediction_max_tokens_per_request: 4096
