# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 15:34:52

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_152628.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 9.344s
- **Vidur 平均 e2e 时间**: 20.271s
- **整体平均差异**: 10.927s (+116.9%)

### 关键发现
- **平均绝对相对差异**: 112.0%
- **相对差异标准差**: 37.6%
- **差异范围**: -63.2% 到 +164.5%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 1 条 | 0.1% | 优秀 |
| 5-10% | 3 条 | 0.3% | 良好 |
| 10-20% | 8 条 | 0.8% | 一般 |
| ≥ 20% | 983 条 | 98.8% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 120.0%
- **P75**: 137.4%
- **P90**: 146.5%
- **P95**: 150.4%
- **P99**: 158.6%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 110.8%）
2. **差异稳定**: 标准差 37.6%，说明偏差相对稳定
3. **影响范围**: 98.8% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 112.0% 超出理想范围。

---
*报告生成时间: 2025-08-14 15:34:52*
