# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 15:29:15

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_152628.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 9.344s
- **Vidur 平均 e2e 时间**: 20.284s
- **整体平均差异**: 10.940s (+117.1%)

### 关键发现
- **平均绝对相对差异**: 111.9%
- **相对差异标准差**: 36.4%
- **差异范围**: -59.8% 到 +164.9%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 2 条 | 0.2% | 优秀 |
| 5-10% | 3 条 | 0.3% | 良好 |
| 10-20% | 8 条 | 0.9% | 一般 |
| ≥ 20% | 981 条 | 98.6% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 119.8%
- **P75**: 136.9%
- **P90**: 145.7%
- **P95**: 149.9%
- **P99**: 158.1%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 111.1%）
2. **差异稳定**: 标准差 36.4%，说明偏差相对稳定
3. **影响范围**: 98.6% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 111.9% 超出理想范围。

---
*报告生成时间: 2025-08-14 15:29:15*
