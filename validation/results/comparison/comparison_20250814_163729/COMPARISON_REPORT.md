# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 16:37:32

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_163636.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 994 条
- **vLLM 平均 e2e 时间**: 12.127s
- **Vidur 平均 e2e 时间**: 20.723s
- **整体平均差异**: 8.596s (+70.9%)

### 关键发现
- **平均绝对相对差异**: 69.3%
- **相对差异标准差**: 24.6%
- **差异范围**: -75.3% 到 +101.3%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 3 条 | 0.3% | 优秀 |
| 5-10% | 6 条 | 0.6% | 良好 |
| 10-20% | 27 条 | 2.7% | 一般 |
| ≥ 20% | 958 条 | 96.4% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 77.4%
- **P75**: 83.7%
- **P90**: 87.6%
- **P95**: 91.1%
- **P99**: 94.9%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 68.2%）
2. **差异稳定**: 标准差 24.6%，说明偏差相对稳定
3. **影响范围**: 96.4% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 69.3% 超出理想范围。

---
*报告生成时间: 2025-08-14 16:37:32*
