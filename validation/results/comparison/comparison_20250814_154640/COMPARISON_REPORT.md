# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 15:46:43

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_154310.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 9.078s
- **Vidur 平均 e2e 时间**: 19.615s
- **整体平均差异**: 10.537s (+116.1%)

### 关键发现
- **平均绝对相对差异**: 110.7%
- **相对差异标准差**: 39.1%
- **差异范围**: -54.9% 到 +164.4%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 7 条 | 0.7% | 优秀 |
| 5-10% | 4 条 | 0.4% | 良好 |
| 10-20% | 9 条 | 1.0% | 一般 |
| ≥ 20% | 974 条 | 97.9% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 120.2%
- **P75**: 133.5%
- **P90**: 150.0%
- **P95**: 156.1%
- **P99**: 161.9%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 109.6%）
2. **差异稳定**: 标准差 39.1%，说明偏差相对稳定
3. **影响范围**: 97.9% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 110.7% 超出理想范围。

---
*报告生成时间: 2025-08-14 15:46:43*
