# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 15:58:29

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_155418.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 8.543s
- **Vidur 平均 e2e 时间**: 19.503s
- **整体平均差异**: 10.960s (+128.3%)

### 关键发现
- **平均绝对相对差异**: 122.6%
- **相对差异标准差**: 41.1%
- **差异范围**: -55.4% 到 +167.1%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 1 条 | 0.1% | 优秀 |
| 5-10% | 3 条 | 0.3% | 良好 |
| 10-20% | 4 条 | 0.5% | 一般 |
| ≥ 20% | 986 条 | 99.1% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 139.3%
- **P75**: 152.4%
- **P90**: 159.1%
- **P95**: 161.9%
- **P99**: 164.9%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 122.0%）
2. **差异稳定**: 标准差 41.1%，说明偏差相对稳定
3. **影响范围**: 99.1% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 122.6% 超出理想范围。

---
*报告生成时间: 2025-08-14 15:58:29*
