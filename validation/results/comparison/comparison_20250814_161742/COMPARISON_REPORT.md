# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-14 16:17:45

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250814_161647.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 12.087s
- **Vidur 平均 e2e 时间**: 19.403s
- **整体平均差异**: 7.317s (+60.5%)

### 关键发现
- **平均绝对相对差异**: 59.5%
- **相对差异标准差**: 23.2%
- **差异范围**: -60.6% 到 +134.7%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 3 条 | 0.3% | 优秀 |
| 5-10% | 4 条 | 0.5% | 良好 |
| 10-20% | 16 条 | 1.6% | 一般 |
| ≥ 20% | 971 条 | 97.6% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 67.1%
- **P75**: 76.6%
- **P90**: 81.1%
- **P95**: 82.3%
- **P99**: 86.5%

### 主要观察
1. **系统性高估**: Vidur 系统性地高估了执行时间（平均高 58.6%）
2. **差异稳定**: 标准差 23.2%，说明偏差相对稳定
3. **影响范围**: 97.6% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 59.5% 超出理想范围。

---
*报告生成时间: 2025-08-14 16:17:45*
